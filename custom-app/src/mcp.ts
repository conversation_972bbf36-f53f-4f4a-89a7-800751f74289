import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport as Transport } from '@modelcontextprotocol/sdk/client/stdio.js';
import { ChildProcessWithoutNullStreams, spawn } from 'child_process';

export type McpServerConfig = {
  name: string;
  enabled?: boolean;
  stdio?: { command: string; args?: string[]; env?: Record<string, string> };
  http?: { url: string; headers?: Record<string, string> };
};

export type McpTool = {
  server: string;
  name: string;
  description?: string;
  inputSchema?: any;
};

export class MCPManager {
  private clients = new Map<string, Client>();
  private processes = new Map<string, ChildProcessWithoutNullStreams>();
  private tools = new Map<string, McpTool[]>();

  constructor(private servers: McpServerConfig[]) {}

  async start() {
    for (const s of this.servers) {
      if (s.enabled === false) continue;
      try {
        if (s.stdio) await this.startStdio(s);
        // TODO: ws/http transports when supported/desired
      } catch (e) {
        console.error(`[MCP] Failed to start ${s.name}:`, e);
      }
    }
  }

  async stop() {
    for (const [name, client] of this.clients) {
      try { await client.close(); } catch {}
    }
    for (const [name, proc] of this.processes) {
      try { proc.kill(); } catch {}
    }
    this.clients.clear();
    this.processes.clear();
    this.tools.clear();
  }

  listTools(): McpTool[] {
    return Array.from(this.tools.values()).flat();
  }

  async callTool(server: string, name: string, args: any) {
    const client = this.clients.get(server);
    if (!client) throw new Error(`MCP server not connected: ${server}`);
    return client.callTool({ name, arguments: args });
  }

  private async startStdio(s: McpServerConfig) {
    const proc = spawn(s.stdio!.command, s.stdio!.args || [], {
      env: { ...process.env, ...(s.stdio!.env || {}) },
      stdio: 'pipe'
    });

    const transport = new Transport({
      stdin: proc.stdout,
      stdout: proc.stdin,
    });

    const client = new Client({ transport });
    await client.connect();

    const caps = await client.listTools();
    this.tools.set(s.name, (caps.tools || []).map((t: any) => ({
      server: s.name,
      name: t.name,
      description: t.description,
      inputSchema: t.input_schema
    })));

    this.clients.set(s.name, client);
    this.processes.set(s.name, proc);

    proc.on('exit', (code) => {
      console.warn(`[MCP] Server ${s.name} exited with code ${code}`);
      this.clients.delete(s.name);
      this.processes.delete(s.name);
      this.tools.delete(s.name);
    });
  }
}

