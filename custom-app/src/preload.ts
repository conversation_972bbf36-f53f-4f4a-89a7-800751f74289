import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron';

contextBridge.exposeInMainWorld('api', {
  getSettings: () => ipcRenderer.invoke('get-settings'),
  saveSettings: (partial: any) => ipcRenderer.invoke('save-settings', partial),
  sendChat: (messages: any[]) => ipcRenderer.invoke('send-chat', messages),
  mcp: {
    listTools: () => ipcRenderer.invoke('mcp-list-tools'),
    callTool: (server: string, name: string, args: any) => ipcRenderer.invoke('mcp-call-tool', server, name, args)
  },
  mcpConfig: {
    get: () => ipcRenderer.invoke('get-mcp-config'),
    save: (cfg: any) => ipcRenderer.invoke('save-mcp-config', cfg)
  },
  onOpenMcpConfig: (cb: () => void) => {
    ipcRenderer.on('open-mcp-config', () => cb());
  }
});

