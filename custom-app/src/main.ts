import { app, BrowserWindow, ipc<PERSON>ain } from 'electron';
// Use require for Menu and shell to avoid TS type issues when Electron types are not installed
declare const require: any;
const { Menu, shell } = require('electron');
import path from 'path';
import fs from 'fs';
import { MCPManager, McpServerConfig } from './mcp';

const CONFIG_DIR = path.join(process.env.HOME || process.env.USERPROFILE || '.', '.config', 'Claude');
const CONFIG_PATH = path.join(CONFIG_DIR, 'claude_desktop_config.json');

function getDefaultConfig() {
  return {
    llm: {
      baseUrl: 'https://api.openai.com',
      apiKey: '',
      model: 'gpt-4o-mini'
    },
    mcp: {
      servers: [] as McpServerConfig[]
    }
  } as { llm: any; mcp: { servers: McpServerConfig[] } };
}

function readConfig() {
  try {
    if (!fs.existsSync(CONFIG_PATH)) {
      return getDefaultConfig();
    }
    const text = fs.readFileSync(CONFIG_PATH, 'utf-8');
    const parsed = JSON.parse(text);
    return { ...getDefaultConfig(), ...parsed };
  } catch (e) {
    return getDefaultConfig();
  }
}

function writeConfig(partial: any) {
  const current = readConfig();
  const next = { ...current, ...partial, llm: { ...current.llm, ...(partial.llm || {}) } };
  if (!fs.existsSync(CONFIG_DIR)) fs.mkdirSync(CONFIG_DIR, { recursive: true });
  fs.writeFileSync(CONFIG_PATH, JSON.stringify(next, null, 2), 'utf-8');
  return next;
}

let win: any | null = null;

function createWindow() {
  win = new BrowserWindow({
    width: 900,
    height: 680,
    webPreferences: {
      preload: path.join(__dirname, 'preload.js'),
      nodeIntegration: false,
      contextIsolation: true
    }
  });
  win.loadFile(path.join(__dirname, 'renderer', 'index.html'));
}

function createMenu() {
  const isMac = process.platform === 'darwin';
  const template: any[] = [
    // App menu (Mac)
    ...(isMac ? [{
      label: app.name,
      submenu: [
        { role: 'about' },
        { type: 'separator' },
        { role: 'services' },
        { type: 'separator' },
        { role: 'hide' },
        { role: 'hideOthers' },
        { role: 'unhide' },
        { type: 'separator' },
        { role: 'quit' }
      ]
    }] : []),

    // File menu
    {
      label: 'File',
      submenu: [
        isMac ? { role: 'close' } : { role: 'quit' }
      ]
    },

    // MCP menu (custom)
    {
      label: 'MCP',
      submenu: [
        {
          label: 'Configure MCP…',
          click: () => {
            if (win) {
              win.webContents.send('open-mcp-config');
            }
          }
        },
      ]
    },

    // Edit menu
    {
      label: 'Edit',
      submenu: [
        { role: 'undo' },
        { role: 'redo' },
        { type: 'separator' },
        { role: 'cut' },
        { role: 'copy' },
        { role: 'paste' },
        ...(isMac ? [
          { role: 'pasteAndMatchStyle' as any },
          { role: 'delete' },
          { role: 'selectAll' },
          { type: 'separator' },
          {
            label: 'Speech',
            submenu: [
              { role: 'startSpeaking' },
              { role: 'stopSpeaking' }
            ]
          }
        ] : [
          { role: 'delete' },
          { type: 'separator' },
          { role: 'selectAll' }
        ])
      ]
    },

    // Window menu
    {
      label: 'Window',
      submenu: [
        { role: 'minimize' },
        { role: 'zoom' },
        ...(isMac ? [
          { type: 'separator' },
          { role: 'front' },
          { type: 'separator' },
          { role: 'window' }
        ] : [
          { role: 'close' }
        ])
      ]
    },

    // Help menu
    {
      role: 'help',
      submenu: [
        {
          label: 'Learn More',
          click: async () => {
            await shell.openExternal('https://github.com/LADLABS/claude-desktop-unleasched-debian');
          }
        }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

let mcp: MCPManager | null = null;

app.whenReady().then(async () => {
  createWindow();
  createMenu();

  // Start MCP if configured
  try {
    const cfg = readConfig();
    mcp = new MCPManager(cfg.mcp?.servers || []);
    await mcp.start();
  } catch (e) {
    console.error('Failed to start MCP:', e);
  }

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) createWindow();
  });
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') app.quit();
});

ipcMain.handle('get-settings', async () => {
  return readConfig().llm;
});

ipcMain.handle('save-settings', async (_event: any, partialLlm: any) => {
  const updated = writeConfig({ llm: partialLlm });
  return updated.llm;
});

async function callOpenAIChat(baseUrl: string, apiKey: string, model: string, messages: any[]) {
  const url = new URL('/v1/chat/completions', baseUrl).toString();
  const res = await fetch(url, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ model, messages, stream: false })
  });
  if (!res.ok) {
    const text = await res.text().catch(() => '');
    throw new Error(`API error ${res.status}: ${text}`);
  }
  const json = await res.json();
  const content = json?.choices?.[0]?.message?.content ?? '';
  return { raw: json, content };
}

// MCP IPC bridge (outside of function scope)
ipcMain.handle('mcp-list-tools', async () => {
  return mcp ? mcp.listTools() : [];
});

ipcMain.handle('mcp-call-tool', async (_event: any, server: string, name: string, args: any) => {
  if (!mcp) throw new Error('MCP not initialized');
  return mcp.callTool(server, name, args);
});

ipcMain.handle('get-mcp-config', async () => {
  const cfg = readConfig();
  return cfg.mcp || { servers: [] };
});

ipcMain.handle('save-mcp-config', async (_event: any, mcpConfig: { servers: McpServerConfig[] }) => {
  const updated = writeConfig({ mcp: mcpConfig });
  // Restart MCP with new config
  try {
    if (mcp) await mcp.stop();
  } catch {}
  try {
    mcp = new MCPManager(updated.mcp?.servers || []);
    await mcp.start();
  } catch (e) {
    console.error('Failed to restart MCP:', e);
  }
  return updated.mcp;
});

ipcMain.handle('send-chat', async (_event: any, messages: any[]) => {
  const cfg = readConfig().llm;
  if (!cfg.baseUrl || !cfg.apiKey || !cfg.model) {
    throw new Error('Missing LLM configuration. Please set endpoint, API key, and model in Settings.');
  }
  return await callOpenAIChat(cfg.baseUrl, cfg.apiKey, cfg.model, messages);
});

// Added custom application menu removing the View menu and adding an MCP menu item that opens the MCP configuration editor in the renderer.
// Also fixed misplaced MCP IPC handlers, added new get/save MCP config handlers, and restarted MCP manager on config save.
