import React, { useState, useEffect } from 'react';
import { But<PERSON> } from './ui/button';
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from './ui/card';
import { Plus, Download, Upload, Edit, Trash2 } from 'lucide-react';

interface SavedConfig {
  id: string;
  name: string;
  type: 'LLM' | 'MCP';
  status?: string;
  llm?: any;
  mcp?: any;
  createdAt: number;
}

interface ToolInfo {
  server: string;
  name: string;
  description?: string;
  inputSchema?: any;
}

const LS_KEYS = { cfgs: 'app.cfgs' };

function loadJson<T>(key: string, fallback: T): T {
  try {
    const s = localStorage.getItem(key);
    return s ? JSON.parse(s) as T : fallback;
  } catch {
    return fallback;
  }
}

function saveJson<T>(key: string, data: T) {
  try {
    localStorage.setItem(key, JSON.stringify(data));
  } catch {}
}

function uuid() {
  return Math.random().toString(36).slice(2) + Date.now().toString(36);
}

export function ConfigPanel() {
  const [configs, setConfigs] = useState<SavedConfig[]>([]);
  const [tools, setTools] = useState<ToolInfo[]>([]);

  useEffect(() => {
    setConfigs(loadJson<SavedConfig[]>(LS_KEYS.cfgs, []));
    refreshTools();
  }, []);

  const refreshTools = async () => {
    try {
      if ((window as any).api?.mcp?.listTools) {
        const toolList = await (window as any).api.mcp.listTools();
        setTools(toolList);
      }
    } catch (e) {
      console.error('Failed to load tools:', e);
    }
  };

  const handleAddConfig = async () => {
    try {
      const settings = await (window as any).api.getSettings();
      const name = prompt('Configuration name', 'LLM Config');
      if (!name) return;

      const newConfig: SavedConfig = {
        id: uuid(),
        name,
        type: 'LLM',
        llm: settings,
        createdAt: Date.now()
      };

      const updatedConfigs = [newConfig, ...configs];
      setConfigs(updatedConfigs);
      saveJson(LS_KEYS.cfgs, updatedConfigs);
    } catch (e) {
      console.error('Failed to add config:', e);
    }
  };

  const handleEditConfig = async (config: SavedConfig) => {
    if (config.type === 'LLM' && config.llm) {
      await (window as any).api.saveSettings(config.llm);
    }
    
    const name = prompt('Configuration name', config.name);
    if (name && name !== config.name) {
      const updatedConfigs = configs.map(c => 
        c.id === config.id ? { ...c, name } : c
      );
      setConfigs(updatedConfigs);
      saveJson(LS_KEYS.cfgs, updatedConfigs);
    }
  };

  const handleDeleteConfig = (configId: string) => {
    if (confirm('Delete configuration?')) {
      const updatedConfigs = configs.filter(c => c.id !== configId);
      setConfigs(updatedConfigs);
      saveJson(LS_KEYS.cfgs, updatedConfigs);
    }
  };

  const handleExport = () => {
    const blob = new Blob([JSON.stringify(configs, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'configs.json';
    a.click();
    URL.revokeObjectURL(url);
  };

  const handleImport = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'application/json';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (!file) return;
      
      const reader = new FileReader();
      reader.onload = () => {
        try {
          const imported = JSON.parse(String(reader.result));
          if (Array.isArray(imported)) {
            setConfigs(imported);
            saveJson(LS_KEYS.cfgs, imported);
          }
        } catch (e) {
          console.error('Failed to import configs:', e);
        }
      };
      reader.readAsText(file);
    };
    input.click();
  };

  const handleCallTool = async (tool: ToolInfo) => {
    const input = prompt(`Args for ${tool.name} (JSON)`, '{}');
    if (input == null) return;
    
    try {
      const args = input ? JSON.parse(input) : {};
      const res = await (window as any).api.mcp.callTool(tool.server, tool.name, args);
      alert(typeof res === 'string' ? res : JSON.stringify(res));
    } catch (e: any) {
      alert(`Tool error: ${e.message || e}`);
    }
  };

  return (
    <div className="h-full flex flex-col p-3 space-y-4">
      <div className="flex gap-2">
        <Button size="sm" onClick={handleAddConfig} title="Save current settings as a configuration">
          <Plus className="h-4 w-4" />
        </Button>
        <Button size="sm" variant="outline" onClick={handleExport} title="Export configurations">
          <Download className="h-4 w-4" />
        </Button>
        <Button size="sm" variant="outline" onClick={handleImport} title="Import configurations">
          <Upload className="h-4 w-4" />
        </Button>
      </div>

      <div className="flex-1 overflow-y-auto space-y-2">
        {configs.map((config) => (
          <Card key={config.id} className="p-3">
            <div className="flex items-center justify-between">
              <div className="flex-1 min-w-0">
                <div className="font-medium text-sm truncate">{config.name}</div>
                <div className="text-xs text-muted-foreground">
                  {config.type} {config.status && `• ${config.status}`}
                </div>
              </div>
              <div className="flex gap-1 ml-2">
                <Button size="sm" variant="ghost" onClick={() => handleEditConfig(config)}>
                  <Edit className="h-3 w-3" />
                </Button>
                <Button size="sm" variant="ghost" onClick={() => handleDeleteConfig(config.id)}>
                  <Trash2 className="h-3 w-3" />
                </Button>
              </div>
            </div>
          </Card>
        ))}
      </div>

      <div className="border-t pt-4">
        <h3 className="text-sm font-medium text-muted-foreground uppercase tracking-wide mb-2">Tools</h3>
        <div className="space-y-1 max-h-32 overflow-y-auto">
          {tools.map((tool, index) => (
            <Button
              key={`${tool.server}-${tool.name}-${index}`}
              variant="ghost"
              size="sm"
              className="w-full justify-start text-xs"
              onClick={() => handleCallTool(tool)}
              title={tool.description || ''}
            >
              {tool.name}
            </Button>
          ))}
        </div>
      </div>
    </div>
  );
}
