import React, { useState, useEffect } from 'react';
import { Tabs, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from './ui/tabs';
import { ConfigPanel } from './ConfigPanel';
import { HistoryPanel } from './HistoryPanel';
import { cn } from '../lib/utils';

interface SidePanelProps {
  isOpen: boolean;
  className?: string;
}

export function SidePanel({ isOpen, className }: SidePanelProps) {
  const [activeTab, setActiveTab] = useState('config');

  return (
    <aside 
      className={cn(
        "bg-card border-r border-border overflow-hidden",
        className
      )}
    >
      <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
        <TabsList className="grid w-full grid-cols-2 m-2">
          <TabsTrigger value="config">Configs</TabsTrigger>
          <TabsTrigger value="history">History</TabsTrigger>
        </TabsList>
        
        <TabsContent value="config" className="flex-1 overflow-hidden m-0">
          <ConfigPanel />
        </TabsContent>
        
        <TabsContent value="history" className="flex-1 overflow-hidden m-0">
          <HistoryPanel />
        </TabsContent>
      </Tabs>
    </aside>
  );
}
