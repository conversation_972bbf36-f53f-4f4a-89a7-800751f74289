import React, { useState } from 'react';
import { Button } from './ui/button';
import { Menu, X } from 'lucide-react';

interface TitleBarProps {
  isPanelOpen: boolean;
  onTogglePanel: () => void;
  onOpenSettings: () => void;
  onOpenMcpConfig: () => void;
}

interface MenuDropdownProps {
  trigger: string;
  children: React.ReactNode;
}

function MenuDropdown({ trigger, children }: MenuDropdownProps) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div 
      className="relative"
      onMouseEnter={() => setIsOpen(true)}
      onMouseLeave={() => setIsOpen(false)}
    >
      <button className="px-3 py-1.5 text-sm text-foreground/90 hover:text-foreground hover:bg-accent/50 rounded-md transition-colors">
        {trigger}
      </button>
      {isOpen && (
        <div className="absolute top-full left-0 mt-1 min-w-[180px] bg-popover border border-border rounded-lg shadow-lg py-1 z-50">
          {children}
        </div>
      )}
    </div>
  );
}

function MenuEntry({ children, onClick }: { children: React.ReactNode; onClick?: () => void }) {
  return (
    <button 
      className="w-full text-left px-3 py-2 text-sm text-foreground hover:bg-accent/50 transition-colors"
      onClick={onClick}
    >
      {children}
    </button>
  );
}

function MenuSeparator() {
  return <div className="h-px bg-border mx-2 my-1" />;
}

export function TitleBar({ isPanelOpen, onTogglePanel, onOpenSettings, onOpenMcpConfig }: TitleBarProps) {
  const handleMenuAction = async (action: string) => {
    if ((window as any).api?.menu?.action) {
      await (window as any).api.menu.action(action);
    }
  };

  return (
    <header className="app-titlebar h-10 flex items-center px-3 bg-card border-b border-border">
      <Button
        variant="ghost"
        size="icon"
        className="h-8 w-8 mr-2"
        onClick={onTogglePanel}
        title="Toggle panel"
      >
        {isPanelOpen ? <X className="h-4 w-4" /> : <Menu className="h-4 w-4" />}
      </Button>

      <div className="flex gap-4">
        <MenuDropdown trigger="File">
          <MenuEntry onClick={() => handleMenuAction('file:new')}>New Chat</MenuEntry>
          <MenuSeparator />
          <MenuEntry onClick={() => handleMenuAction('file:quit')}>Quit</MenuEntry>
        </MenuDropdown>

        <MenuDropdown trigger="Edit">
          <MenuEntry onClick={() => handleMenuAction('edit:undo')}>Undo</MenuEntry>
          <MenuEntry onClick={() => handleMenuAction('edit:redo')}>Redo</MenuEntry>
          <MenuSeparator />
          <MenuEntry onClick={() => handleMenuAction('edit:cut')}>Cut</MenuEntry>
          <MenuEntry onClick={() => handleMenuAction('edit:copy')}>Copy</MenuEntry>
          <MenuEntry onClick={() => handleMenuAction('edit:paste')}>Paste</MenuEntry>
        </MenuDropdown>

        <MenuDropdown trigger="Settings">
          <MenuEntry onClick={onOpenSettings}>Preferences…</MenuEntry>
          <MenuEntry onClick={onOpenMcpConfig}>MCP Configuration…</MenuEntry>
        </MenuDropdown>

        <MenuDropdown trigger="Help">
          <MenuEntry onClick={() => handleMenuAction('help:learnMore')}>Learn More</MenuEntry>
        </MenuDropdown>
      </div>

      <div className="flex-1" />

      <div className="flex items-center gap-2 text-sm text-muted-foreground font-medium">
        <span className="text-foreground">Claude</span>
        <div className="w-4 h-4 rounded-full bg-primary/80 shadow-sm" />
      </div>
    </header>
  );
}
