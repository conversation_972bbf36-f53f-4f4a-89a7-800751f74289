import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogBody,
  DialogFooter,
} from './ui/dialog';
import { Button } from './ui/button';
import { Textarea } from './ui/textarea';

interface McpConfigDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function McpConfigDialog({ open, onOpenChange }: McpConfigDialogProps) {
  const [configJson, setConfigJson] = useState('');
  const [status, setStatus] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (open) {
      loadConfig();
    }
  }, [open]);

  const loadConfig = async () => {
    try {
      if ((window as any).api?.mcpConfig?.get) {
        const config = await (window as any).api.mcpConfig.get();
        setConfigJson(JSON.stringify(config, null, 2));
      }
    } catch (error) {
      console.error('Failed to load MCP config:', error);
      setConfigJson('{\n  "servers": []\n}');
    }
  };

  const handleSave = async () => {
    try {
      setIsLoading(true);
      setStatus('Saving...');
      
      const parsed = JSON.parse(configJson);
      
      if ((window as any).api?.mcpConfig?.save) {
        await (window as any).api.mcpConfig.save(parsed);
        setStatus('Saved successfully! MCP restarted.');
        setTimeout(() => setStatus(''), 3000);
      }
    } catch (error: any) {
      if (error instanceof SyntaxError) {
        setStatus('Invalid JSON format');
      } else {
        setStatus(`Error: ${error.message || error}`);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    setStatus('');
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[80vh]">
        <DialogHeader>
          <DialogTitle>MCP Configuration</DialogTitle>
          <DialogDescription>
            Edit the MCP configuration JSON. This updates ~/.config/Claude/claude_desktop_config.json and restarts MCP.
          </DialogDescription>
        </DialogHeader>

        <DialogBody className="space-y-4">
          <Textarea
            value={configJson}
            onChange={(e) => setConfigJson(e.target.value)}
            className="min-h-[300px] font-mono text-sm"
            placeholder="Enter MCP configuration JSON..."
          />
        </DialogBody>

        {status && (
          <div className="text-sm text-center py-2 text-muted-foreground">
            {status}
          </div>
        )}

        <DialogFooter className="gap-2">
          <Button
            onClick={handleSave}
            disabled={isLoading}
          >
            Save
          </Button>
          <Button
            variant="outline"
            onClick={handleClose}
          >
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
