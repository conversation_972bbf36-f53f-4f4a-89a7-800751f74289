import React, { useState, useEffect } from 'react';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Card } from './ui/card';
import { Download, Upload, Edit, Trash2, X } from 'lucide-react';

interface HistoryItem {
  id: string;
  text: string;
  model?: string;
  ts: number;
}

const LS_KEYS = { history: 'app.history' };

function loadJson<T>(key: string, fallback: T): T {
  try {
    const s = localStorage.getItem(key);
    return s ? JSON.parse(s) as T : fallback;
  } catch {
    return fallback;
  }
}

function saveJson<T>(key: string, data: T) {
  try {
    localStorage.setItem(key, JSON.stringify(data));
  } catch {}
}

export function HistoryPanel() {
  const [history, setHistory] = useState<HistoryItem[]>([]);
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    setHistory(loadJson<HistoryItem[]>(LS_KEYS.history, []));
  }, []);

  const filteredHistory = history.filter(item => 
    !searchQuery || item.text.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleEditItem = (item: HistoryItem) => {
    const newText = prompt('Edit prompt', item.text);
    if (newText != null) {
      const updatedHistory = history.map(h => 
        h.id === item.id ? { ...h, text: newText } : h
      );
      setHistory(updatedHistory);
      saveJson(LS_KEYS.history, updatedHistory);
    }
  };

  const handleDeleteItem = (itemId: string) => {
    if (confirm('Delete prompt?')) {
      const updatedHistory = history.filter(h => h.id !== itemId);
      setHistory(updatedHistory);
      saveJson(LS_KEYS.history, updatedHistory);
    }
  };

  const handleExport = () => {
    const blob = new Blob([JSON.stringify(history, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'history.json';
    a.click();
    URL.revokeObjectURL(url);
  };

  const handleImport = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'application/json';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (!file) return;
      
      const reader = new FileReader();
      reader.onload = () => {
        try {
          const imported = JSON.parse(String(reader.result));
          if (Array.isArray(imported)) {
            setHistory(imported);
            saveJson(LS_KEYS.history, imported);
          }
        } catch (e) {
          console.error('Failed to import history:', e);
        }
      };
      reader.readAsText(file);
    };
    input.click();
  };

  const handleClearAll = () => {
    if (confirm('Clear all history?')) {
      setHistory([]);
      saveJson(LS_KEYS.history, []);
    }
  };

  return (
    <div className="h-full flex flex-col p-3 space-y-4">
      <div className="flex gap-2">
        <Input
          placeholder="Search history..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="flex-1"
        />
        {searchQuery && (
          <Button size="sm" variant="ghost" onClick={() => setSearchQuery('')}>
            <X className="h-4 w-4" />
          </Button>
        )}
      </div>

      <div className="flex gap-2">
        <Button size="sm" variant="outline" onClick={handleExport} title="Export history">
          <Download className="h-4 w-4" />
        </Button>
        <Button size="sm" variant="outline" onClick={handleImport} title="Import history">
          <Upload className="h-4 w-4" />
        </Button>
        <Button size="sm" variant="destructive" onClick={handleClearAll} title="Clear all history">
          <Trash2 className="h-4 w-4" />
        </Button>
      </div>

      <div className="flex-1 overflow-y-auto space-y-2">
        {filteredHistory.map((item) => (
          <Card key={item.id} className="p-3">
            <div className="space-y-2">
              <div className="text-sm line-clamp-3">{item.text}</div>
              <div className="flex items-center justify-between">
                <div className="text-xs text-muted-foreground">
                  {new Date(item.ts).toLocaleString()} {item.model && `• ${item.model}`}
                </div>
                <div className="flex gap-1">
                  <Button size="sm" variant="ghost" onClick={() => handleEditItem(item)}>
                    <Edit className="h-3 w-3" />
                  </Button>
                  <Button size="sm" variant="ghost" onClick={() => handleDeleteItem(item.id)}>
                    <Trash2 className="h-3 w-3" />
                  </Button>
                </div>
              </div>
            </div>
          </Card>
        ))}
        
        {filteredHistory.length === 0 && (
          <div className="text-center text-muted-foreground text-sm py-8">
            {searchQuery ? 'No matching history found' : 'No history yet'}
          </div>
        )}
      </div>
    </div>
  );
}
