import React, { useState, useEffect, useRef } from 'react';
import { Button } from './ui/button';
import { Textarea } from './ui/textarea';
import { Card } from './ui/card';
import { Send } from 'lucide-react';
import { cn } from '../lib/utils';

interface Message {
  role: 'user' | 'assistant' | 'tool';
  content: string;
}

interface HistoryItem {
  id: string;
  text: string;
  model?: string;
  ts: number;
}

interface ChatAreaProps {
  className?: string;
}

const LS_KEYS = { history: 'app.history' };

function loadJson<T>(key: string, fallback: T): T {
  try {
    const s = localStorage.getItem(key);
    return s ? JSON.parse(s) as T : fallback;
  } catch {
    return fallback;
  }
}

function saveJson<T>(key: string, data: T) {
  try {
    localStorage.setItem(key, JSON.stringify(data));
  } catch {}
}

function uuid() {
  return Math.random().toString(36).slice(2) + Date.now().toString(36);
}

export function ChatArea({ className }: ChatAreaProps) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputText, setInputText] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const addMessage = (role: Message['role'], content: string) => {
    setMessages(prev => [...prev, { role, content }]);
  };

  const handleSend = async () => {
    if (!inputText.trim() || isLoading) return;

    const text = inputText.trim();
    setInputText('');
    setIsLoading(true);

    addMessage('user', text);

    try {
      const response = await (window as any).api.sendChat([{ role: 'user', content: text }]);
      addMessage('assistant', response.content || '[no content]');

      // Save to history
      const history = loadJson<HistoryItem[]>(LS_KEYS.history, []);
      const settings = await (window as any).api.getSettings();
      const newHistoryItem: HistoryItem = {
        id: uuid(),
        text,
        model: settings.model,
        ts: Date.now()
      };
      history.unshift(newHistoryItem);
      saveJson(LS_KEYS.history, history);

    } catch (error: any) {
      addMessage('assistant', `Error: ${error.message || error}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  return (
    <div className={cn("flex flex-col h-full", className)}>
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message, index) => (
          <div
            key={index}
            className={cn(
              "flex",
              message.role === 'user' ? "justify-end" : "justify-start"
            )}
          >
            <Card
              className={cn(
                "max-w-[70%] p-4",
                message.role === 'user' 
                  ? "bg-primary text-primary-foreground" 
                  : "bg-muted",
                message.role === 'tool' && "bg-accent text-accent-foreground"
              )}
            >
              <div className="whitespace-pre-wrap text-sm">
                {message.content}
              </div>
            </Card>
          </div>
        ))}
        
        {isLoading && (
          <div className="flex justify-start">
            <Card className="bg-muted p-4">
              <div className="flex items-center space-x-2">
                <div className="animate-pulse">Thinking...</div>
              </div>
            </Card>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      <div className="border-t border-border p-4">
        <div className="flex gap-2">
          <Textarea
            value={inputText}
            onChange={(e) => setInputText(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Type your message..."
            className="flex-1 min-h-[80px] resize-none"
            disabled={isLoading}
          />
          <Button
            onClick={handleSend}
            disabled={!inputText.trim() || isLoading}
            size="icon"
            className="self-end"
          >
            <Send className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}
