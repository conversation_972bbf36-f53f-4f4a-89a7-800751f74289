import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogBody,
  DialogFooter,
} from './ui/dialog';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';

interface SettingsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

interface LLMSettings {
  baseUrl: string;
  apiKey: string;
  model: string;
}

export function SettingsDialog({ open, onOpenChange }: SettingsDialogProps) {
  const [settings, setSettings] = useState<LLMSettings>({
    baseUrl: '',
    apiKey: '',
    model: ''
  });
  const [status, setStatus] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (open) {
      loadSettings();
    }
  }, [open]);

  const loadSettings = async () => {
    try {
      const currentSettings = await (window as any).api.getSettings();
      setSettings({
        baseUrl: currentSettings.baseUrl || '',
        apiKey: currentSettings.apiKey || '',
        model: currentSettings.model || ''
      });
    } catch (error) {
      console.error('Failed to load settings:', error);
    }
  };

  const handleSave = async () => {
    try {
      setIsLoading(true);
      setStatus('Saving...');
      
      await (window as any).api.saveSettings({
        baseUrl: settings.baseUrl.trim(),
        apiKey: settings.apiKey.trim(),
        model: settings.model.trim()
      });
      
      setStatus('Saved successfully!');
      setTimeout(() => setStatus(''), 2000);
    } catch (error: any) {
      setStatus(`Error: ${error.message || error}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleTest = async () => {
    try {
      setIsLoading(true);
      setStatus('Testing connection...');
      
      await (window as any).api.sendChat([{ role: 'user', content: 'ping' }]);
      setStatus('Connection successful!');
      setTimeout(() => setStatus(''), 2000);
    } catch (error: any) {
      setStatus(`Connection failed: ${error.message || error}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    setStatus('');
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Settings</DialogTitle>
          <DialogDescription>
            Configure the LLM endpoint and other application settings.
          </DialogDescription>
        </DialogHeader>

        <DialogBody className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">LLM Configuration</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Endpoint URL</label>
                <Input
                  value={settings.baseUrl}
                  onChange={(e) => setSettings(prev => ({ ...prev, baseUrl: e.target.value }))}
                  placeholder="https://api.openai.com"
                />
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">API Key</label>
                <Input
                  type="password"
                  value={settings.apiKey}
                  onChange={(e) => setSettings(prev => ({ ...prev, apiKey: e.target.value }))}
                  placeholder="sk-..."
                />
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Model</label>
                <Input
                  value={settings.model}
                  onChange={(e) => setSettings(prev => ({ ...prev, model: e.target.value }))}
                  placeholder="gpt-4o-mini"
                />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">MCP</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Configured via ~/.config/Claude/claude_desktop_config.json
              </p>
            </CardContent>
          </Card>
        </DialogBody>

        {status && (
          <div className="text-sm text-center py-2 text-muted-foreground">
            {status}
          </div>
        )}

        <DialogFooter className="gap-2">
          <Button
            variant="outline"
            onClick={handleTest}
            disabled={isLoading}
          >
            Test Connection
          </Button>
          <Button
            onClick={handleSave}
            disabled={isLoading}
          >
            Save
          </Button>
          <Button
            variant="outline"
            onClick={handleClose}
          >
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
