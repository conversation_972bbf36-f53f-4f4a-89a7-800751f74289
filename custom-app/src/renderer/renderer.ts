type Role = 'system' | 'user' | 'assistant' | 'tool';

type ToolInfo = { server: string; name: string; description?: string; inputSchema?: any };

const chatEl = document.getElementById('chat') as HTMLDivElement;
const messageEl = document.getElementById('message') as HTMLTextAreaElement;
const sendBtn = document.getElementById('send') as HTMLButtonElement;
const openSettingsBtn = document.getElementById('open-settings') as HTMLButtonElement;

const toolsList = document.getElementById('tools') as HTMLUListElement;

const modal = document.getElementById('settings-modal') as HTMLDivElement;
const endpointEl = document.getElementById('endpoint') as HTMLInputElement;
const apiKeyEl = document.getElementById('apikey') as HTMLInputElement;
const modelEl = document.getElementById('model') as HTMLInputElement;
const saveBtn = document.getElementById('save') as HTMLButtonElement;
const testBtn = document.getElementById('test') as HTMLButtonElement;
const closeBtn = document.getElementById('close') as HTMLButtonElement;
const statusEl = document.getElementById('settings-status') as HTMLDivElement;

// MCP config modal elements
const mcpModal = document.getElementById('mcp-modal') as HTMLDivElement | null;
const mcpJsonEl = document.getElementById('mcp-json') as HTMLTextAreaElement | null;
const mcpSaveBtn = document.getElementById('mcp-save') as HTMLButtonElement | null;
const mcpCloseBtn = document.getElementById('mcp-close') as HTMLButtonElement | null;
const mcpStatusEl = document.getElementById('mcp-status2') as HTMLDivElement | null;

function appendMessage(role: Role, content: string) {
  const div = document.createElement('div');
  div.className = `msg ${role}`;
  div.textContent = content;
  chatEl.appendChild(div);
  chatEl.scrollTop = chatEl.scrollHeight;
}

async function loadSettings() {
  const s = await (window as any).api.getSettings();
  endpointEl.value = s.baseUrl || '';
  apiKeyEl.value = s.apiKey || '';
  modelEl.value = s.model || '';
}

async function refreshTools() {
  try {
    const tools: ToolInfo[] = await (window as any).api.mcp.listTools();
    toolsList.innerHTML = '';
    const byServer: Record<string, ToolInfo[]> = {};
    for (const t of tools) (byServer[t.server] ||= []).push(t);
    for (const server of Object.keys(byServer)) {
      const header = document.createElement('li');
      header.textContent = server;
      header.style.fontWeight = '600';
      header.style.marginTop = '8px';
      toolsList.appendChild(header);
      for (const t of byServer[server]) {
        const li = document.createElement('li');
        const btn = document.createElement('button');
        btn.className = 'btn';
        btn.textContent = t.name;
        btn.title = t.description || '';
        btn.addEventListener('click', async () => {
          const input = prompt(`Args for ${t.name} (JSON)` , '{}');
          if (input == null) return;
          try {
            const args = input ? JSON.parse(input) : {};
            appendMessage('tool', `▶ ${t.server}/${t.name} ${JSON.stringify(args)}`);
            const res = await (window as any).api.mcp.callTool(t.server, t.name, args);
            appendMessage('assistant', typeof res === 'string' ? res : JSON.stringify(res));
          } catch (e: any) {
            appendMessage('assistant', `Tool error: ${e.message || e}`);
          }
        });
        li.appendChild(btn);
        toolsList.appendChild(li);
      }
    }
  } catch (e) {
    console.error(e);
  }
}

function showModal() { modal.classList.remove('hidden'); }
function hideModal() { modal.classList.add('hidden'); statusEl.textContent = ''; }

openSettingsBtn.addEventListener('click', async () => {
  await loadSettings();
  showModal();
});

closeBtn.addEventListener('click', () => hideModal());

saveBtn.addEventListener('click', async () => {
  const baseUrl = endpointEl.value.trim();
  const apiKey = apiKeyEl.value.trim();
  const model = modelEl.value.trim();
  await (window as any).api.saveSettings({ baseUrl, apiKey, model });
  statusEl.textContent = 'Saved.';
});

async function testConnection() {
  statusEl.textContent = 'Testing...';
  try {
    await (window as any).api.sendChat([{ role: 'user', content: 'ping' }]);
    statusEl.textContent = 'Connection OK';
  } catch (e: any) {
    statusEl.textContent = `Error: ${e.message || e}`;
  }
}

// Initial data
void refreshTools();

testBtn.addEventListener('click', () => { void testConnection(); });

sendBtn.addEventListener('click', async () => {
  const text = messageEl.value.trim();
  if (!text) return;
  appendMessage('user', text);
  messageEl.value = '';
  try {
    const res = await (window as any).api.sendChat([{ role: 'user', content: text }]);
    appendMessage('assistant', res.content || '[no content]');
  } catch (e: any) {
    appendMessage('assistant', `Error: ${e.message || e}`);
  }
});


// MCP Config modal logic
function showMcpModal() { if (mcpModal) mcpModal.classList.remove('hidden'); }
function hideMcpModal() { if (mcpModal && mcpStatusEl) { mcpModal.classList.add('hidden'); mcpStatusEl.textContent = ''; } }

if ((window as any).api?.onOpenMcpConfig) {
  (window as any).api.onOpenMcpConfig(async () => {
    if (!mcpJsonEl) return;
    try {
      const cfg = await (window as any).api.mcpConfig.get();
      mcpJsonEl.value = JSON.stringify(cfg, null, 2);
    } catch (e: any) {
      mcpJsonEl.value = '{\n  "servers": []\n}';
    }
    showMcpModal();
  });
}

if (mcpCloseBtn) mcpCloseBtn.addEventListener('click', () => hideMcpModal());
if (mcpSaveBtn) mcpSaveBtn.addEventListener('click', async () => {
  if (!mcpJsonEl || !mcpStatusEl) return;
  try {
    const parsed = JSON.parse(mcpJsonEl.value);
    mcpStatusEl.textContent = 'Saving...';
    await (window as any).api.mcpConfig.save(parsed);
    mcpStatusEl.textContent = 'Saved. Restarted MCP.';
    // Refresh tools list after MCP restart
    await refreshTools();
  } catch (e: any) {
    mcpStatusEl.textContent = `Error: ${e.message || e}`;
  }
});

