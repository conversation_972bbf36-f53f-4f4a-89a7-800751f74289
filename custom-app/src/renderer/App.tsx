import React, { useState, useEffect } from 'react';
import { TitleBar } from './components/TitleBar';
import { SidePanel } from './components/SidePanel';
import { ChatArea } from './components/ChatArea';
import { SettingsDialog } from './components/SettingsDialog';
import { McpConfigDialog } from './components/McpConfigDialog';

export function App() {
  const [isPanelOpen, setIsPanelOpen] = useState(true);
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);
  const [isMcpConfigOpen, setIsMcpConfigOpen] = useState(false);

  useEffect(() => {
    // Listen for MCP config open events from main process
    if ((window as any).api?.onOpenMcpConfig) {
      (window as any).api.onOpenMcpConfig(() => {
        setIsMcpConfigOpen(true);
      });
    }
  }, []);

  return (
    <div className="h-screen flex flex-col bg-background text-foreground">
      <TitleBar
        isPanelOpen={isPanelOpen}
        onTogglePanel={() => setIsPanelOpen(!isPanelOpen)}
        onOpenSettings={() => setIsSettingsOpen(true)}
        onOpenMcpConfig={() => setIsMcpConfigOpen(true)}
      />
      
      <div className="flex flex-1 min-h-0">
        <SidePanel
          isOpen={isPanelOpen}
          className={`transition-all duration-300 ease-in-out ${
            isPanelOpen ? 'w-64' : 'w-0'
          }`}
        />

        <ChatArea
          className="flex-1"
        />
      </div>

      <SettingsDialog 
        open={isSettingsOpen}
        onOpenChange={setIsSettingsOpen}
      />

      <McpConfigDialog 
        open={isMcpConfigOpen}
        onOpenChange={setIsMcpConfigOpen}
      />
    </div>
  );
}
