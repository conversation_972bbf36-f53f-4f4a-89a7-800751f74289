:root {
  --bg: #ffffff;
  --fg: #111111;
  --muted: #666;
  --border: #e5e5e5;
  --accent: #4f46e5;
  --accent-weak: #eef2ff;
}
@media (prefers-color-scheme: dark) {
  :root {
    --bg: #0b0b0c;
    --fg: #f4f4f5;
    --muted: #a1a1aa;
    --border: #27272a;
    --accent: #6366f1;
    --accent-weak: #1f2233;
  }
}
* { box-sizing: border-box; }
body { font-family: ui-sans-serif, system-ui, -apple-system, Segoe UI, Roboto, Noto Sans, Ubuntu, Cantarell, Helvetica Neue, Arial, "Apple Color Emoji", "Segoe UI Emoji"; margin: 0; color: var(--fg); background: var(--bg); height: 100vh; display: flex; flex-direction: column; }
.toolbar { display: flex; align-items: center; gap: 12px; padding: 10px 14px; border-bottom: 1px solid var(--border); }
.toolbar .title { font-weight: 600; }
.toolbar .spacer { flex: 1; }
.layout { flex: 1; display: flex; min-height: 0; }
.sidebar { width: 260px; border-right: 1px solid var(--border); padding: 10px; display: flex; flex-direction: column; gap: 12px; }
.section-title { font-size: 12px; color: var(--muted); text-transform: uppercase; letter-spacing: .06em; }
ul { list-style: none; padding: 0; margin: 0; }
.content { flex: 1; display: flex; flex-direction: column; min-width: 0; }
.chat { flex: 1; overflow-y: auto; padding: 16px; display: flex; flex-direction: column; gap: 10px; }
.composer { display: flex; gap: 8px; padding: 10px; border-top: 1px solid var(--border); }
.composer textarea { flex: 1; height: 88px; padding: 10px; border: 1px solid var(--border); border-radius: 10px; background: transparent; color: var(--fg); }
.btn { padding: 8px 12px; border: 1px solid var(--border); border-radius: 10px; background: transparent; color: var(--fg); cursor: pointer; }
.btn.primary { background: var(--accent); color: white; border-color: var(--accent); }
.msg { max-width: 70%; padding: 10px 12px; border-radius: 14px; white-space: pre-wrap; }
.msg.user { align-self: flex-end; margin-left: auto; background: var(--accent-weak); }
.msg.assistant { align-self: flex-start; margin-right: auto; background: #f2f2f2; }
@media (prefers-color-scheme: dark) { .msg.assistant { background: #161617; } }
.modal { position: fixed; inset: 0; background: rgba(0,0,0,0.35); display: flex; align-items: center; justify-content: center; }
.hidden { display: none; }
.modal-content { background: var(--bg); color: var(--fg); padding: 18px; border-radius: 12px; width: 560px; display: flex; flex-direction: column; gap: 12px; border: 1px solid var(--border); }
.modal-content .settings-section { border-top: 1px solid var(--border); padding-top: 8px; }
.modal-content label { display: flex; flex-direction: column; gap: 6px; }
.actions { display: flex; gap: 8px; justify-content: flex-end; }
#settings-status { min-height: 20px; font-size: 12px; color: var(--muted); }

