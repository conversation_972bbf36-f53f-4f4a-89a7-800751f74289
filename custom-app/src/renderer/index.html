<!doctype html>
<html>
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>LLM Client</title>
  <link rel="stylesheet" href="styles.css" />
</head>
<body>
  <header class="app-titlebar">
    <button id="panel-toggle" class="titlebar-icon" title="Toggle panel" aria-label="Toggle panel">☰</button>
    <div class="menu-bar" role="menubar">
      <div class="menu-item" data-menu="file" role="menuitem" tabindex="0">File
        <div class="menu-dropdown" id="menu-file" role="menu">
          <button class="menu-entry" id="file-new">New Chat</button>
          <button class="menu-entry" id="file-quit">Quit</button>
        </div>
      </div>
      <div class="menu-item" data-menu="edit" role="menuitem" tabindex="0">Edit
        <div class="menu-dropdown" id="menu-edit" role="menu">
          <button class="menu-entry" id="edit-undo">Undo</button>
          <button class="menu-entry" id="edit-redo">Redo</button>
          <div class="menu-sep"></div>
          <button class="menu-entry" id="edit-cut">Cut</button>
          <button class="menu-entry" id="edit-copy">Copy</button>
          <button class="menu-entry" id="edit-paste">Paste</button>
        </div>
      </div>
      <div class="menu-item" data-menu="settings" role="menuitem" tabindex="0">Settings
        <div class="menu-dropdown" id="menu-settings" role="menu">
          <button class="menu-entry" id="open-settings">Preferences…</button>
          <button class="menu-entry" id="open-mcp-config">MCP Configuration…</button>
        </div>
      </div>
      <div class="menu-item" data-menu="help" role="menuitem" tabindex="0">Help
        <div class="menu-dropdown" id="menu-help" role="menu">
          <button class="menu-entry" id="help-learn">Learn More</button>
        </div>
      </div>
    </div>
    <div class="title-spacer"></div>
    <div class="brand">
      <span class="brand-text">Claude</span>
      <svg class="brand-mark" width="16" height="16" viewBox="0 0 24 24" aria-hidden="true"><circle cx="12" cy="12" r="8" fill="#a78bfa"/></svg>
    </div>
  </header>
  <div class="layout">
    <aside class="sidebar">
      <div class="panel-tabs" role="tablist">
        <button class="tab active" id="tab-config" role="tab" aria-controls="panel-config">Configs</button>
        <button class="tab" id="tab-history" role="tab" aria-controls="panel-history">History</button>
      </div>
      <div class="panel-content" id="panel-config" role="tabpanel">
        <div class="panel-actions">
          <button class="btn" id="cfg-add" title="Save current settings as a configuration">Add</button>
          <button class="btn" id="cfg-export" title="Export configurations">Export</button>
          <input type="file" id="cfg-import-file" accept="application/json" style="display:none" />
          <button class="btn" id="cfg-import" title="Import configurations">Import</button>
        </div>
        <ul id="cfg-list"></ul>
        <div class="section">
          <div class="section-title">Tools</div>
          <ul id="tools"></ul>
        </div>
      </div>
      <div class="panel-content hidden" id="panel-history" role="tabpanel">
        <div class="panel-actions">
          <input id="hist-search" placeholder="Search history..." aria-label="Search history" />
          <button class="btn" id="hist-export" title="Export history">Export</button>
          <input type="file" id="hist-import-file" accept="application/json" style="display:none" />
          <button class="btn" id="hist-import" title="Import history">Import</button>
          <button class="btn" id="hist-clear" title="Clear all history">Clear</button>
        </div>
        <ul id="hist-list"></ul>
      </div>
    </aside>
    <main class="content">
      <div id="chat" class="chat"></div>
      <div id="input" class="composer">
        <textarea id="message" placeholder="Type your message..."></textarea>
        <button id="send" class="btn primary">Send</button>
      </div>
    </main>
  </div>

  <div id="settings-modal" class="modal hidden">
    <div class="modal-content">
      <h2>Settings</h2>
      <div class="settings-section">
        <h3>LLM</h3>
        <label>
          Endpoint URL
          <input type="text" id="endpoint" placeholder="https://api.openai.com" />
        </label>
        <label>
          API Key
          <input type="password" id="apikey" placeholder="sk-..." />
        </label>
        <label>
          Model
          <input type="text" id="model" placeholder="gpt-4o-mini" />
        </label>
      </div>
      <div class="settings-section">
        <h3>MCP</h3>
        <div id="mcp-status">Configured via ~/.config/Claude/claude_desktop_config.json</div>
        <ul id="mcp-servers"></ul>
      </div>
      <div class="actions">
        <button id="test" class="btn">Test</button>
        <button id="save" class="btn primary">Save</button>
        <button id="close" class="btn">Close</button>
      </div>
      <div id="settings-status"></div>
    </div>
  </div>

  <!-- MCP Configuration Modal -->
  <div id="mcp-modal" class="modal hidden">
    <div class="modal-content">
      <h2>MCP Configuration</h2>
      <p>Edit the MCP configuration JSON. This updates ~/.config/Claude/claude_desktop_config.json and restarts MCP.</p>
      <textarea id="mcp-json" style="width:100%; height: 300px; font-family: monospace;"></textarea>
      <div class="actions">
        <button id="mcp-save" class="btn primary">Save</button>
        <button id="mcp-close" class="btn">Close</button>
      </div>
      <div id="mcp-status2"></div>
    </div>
  </div>

  <script src="renderer.js"></script>
</body>
</html>

