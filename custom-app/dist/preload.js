"use strict";

// src/preload.ts
var import_electron = require("electron");
import_electron.contextBridge.exposeInMainWorld("api", {
  getSettings: () => import_electron.ipcRenderer.invoke("get-settings"),
  saveSettings: (partial) => import_electron.ipcRenderer.invoke("save-settings", partial),
  sendChat: (messages) => import_electron.ipcRenderer.invoke("send-chat", messages),
  mcp: {
    listTools: () => import_electron.ipcRenderer.invoke("mcp-list-tools"),
    callTool: (server, name, args) => import_electron.ipcRenderer.invoke("mcp-call-tool", server, name, args)
  },
  mcpConfig: {
    get: () => import_electron.ipcRenderer.invoke("get-mcp-config"),
    save: (cfg) => import_electron.ipcRenderer.invoke("save-mcp-config", cfg)
  },
  onOpenMcpConfig: (cb) => {
    import_electron.ipcRenderer.on("open-mcp-config", () => cb());
  }
});
