"use strict";

const chatEl = document.getElementById('chat') ;
const messageEl = document.getElementById('message') ;
const sendBtn = document.getElementById('send') ;
const openSettingsBtn = document.getElementById('open-settings') ;

const modal = document.getElementById('settings-modal') ;
const endpointEl = document.getElementById('endpoint') ;
const apiKeyEl = document.getElementById('apikey') ;
const modelEl = document.getElementById('model') ;
const saveBtn = document.getElementById('save') ;
const testBtn = document.getElementById('test') ;
const closeBtn = document.getElementById('close') ;
const statusEl = document.getElementById('settings-status') ;

function appendMessage(role, content) {
  const div = document.createElement('div');
  div.className = `msg ${role}`;
  div.textContent = content;
  chatEl.appendChild(div);
  chatEl.scrollTop = chatEl.scrollHeight;
}

async function loadSettings() {
  const s = await (window ).api.getSettings();
  endpointEl.value = s.baseUrl || '';
  apiKeyEl.value = s.apiKey || '';
  modelEl.value = s.model || '';
}

function showModal() { modal.classList.remove('hidden'); }
function hideModal() { modal.classList.add('hidden'); statusEl.textContent = ''; }

openSettingsBtn.addEventListener('click', async () => {
  await loadSettings();
  showModal();
});

closeBtn.addEventListener('click', () => hideModal());

saveBtn.addEventListener('click', async () => {
  const baseUrl = endpointEl.value.trim();
  const apiKey = apiKeyEl.value.trim();
  const model = modelEl.value.trim();
  await (window ).api.saveSettings({ baseUrl, apiKey, model });
  statusEl.textContent = 'Saved.';
});

async function testConnection() {
  statusEl.textContent = 'Testing...';
  try {
    // Send a minimal harmless prompt
    await (window ).api.sendChat([{ role: 'user', content: 'ping' }]);
    statusEl.textContent = 'Connection OK';
  } catch (e) {
    statusEl.textContent = `Error: ${e.message || e}`;
  }
}

testBtn.addEventListener('click', () => { void testConnection(); });

sendBtn.addEventListener('click', async () => {
  const text = messageEl.value.trim();
  if (!text) return;
  appendMessage('user', text);
  messageEl.value = '';
  try {
    const res = await (window ).api.sendChat([{ role: 'user', content: text }]);
    appendMessage('assistant', res.content || '[no content]');
  } catch (e) {
    appendMessage('assistant', `Error: ${e.message || e}`);
  }
});

