<!doctype html>
<html>
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>LLM Client</title>
  <link rel="stylesheet" href="styles.css" />
</head>
<body>
  <header class="toolbar">
    <div class="title">Claude Desktop Unleashed</div>
    <div class="spacer"></div>
    <div class="status" id="status">Ready</div>
    <button id="open-settings" class="btn">Settings</button>
  </header>
  <div class="layout">
    <aside class="sidebar">
      <div class="section">
        <div class="section-title">Tools</div>
        <ul id="tools"></ul>
      </div>
    </aside>
    <main class="content">
      <div id="chat" class="chat"></div>
      <div id="input" class="composer">
        <textarea id="message" placeholder="Type your message..."></textarea>
        <button id="send" class="btn primary">Send</button>
      </div>
    </main>
  </div>

  <div id="settings-modal" class="modal hidden">
    <div class="modal-content">
      <h2>Settings</h2>
      <div class="settings-section">
        <h3>LLM</h3>
        <label>
          Endpoint URL
          <input type="text" id="endpoint" placeholder="https://api.openai.com" />
        </label>
        <label>
          API Key
          <input type="password" id="apikey" placeholder="sk-..." />
        </label>
        <label>
          Model
          <input type="text" id="model" placeholder="gpt-4o-mini" />
        </label>
      </div>
      <div class="settings-section">
        <h3>MCP</h3>
        <div id="mcp-status">Configured via ~/.config/Claude/claude_desktop_config.json</div>
        <ul id="mcp-servers"></ul>
      </div>
      <div class="actions">
        <button id="test" class="btn">Test</button>
        <button id="save" class="btn primary">Save</button>
        <button id="close" class="btn">Close</button>
      </div>
      <div id="settings-status"></div>
    </div>
  </div>

  <!-- MCP Configuration Modal -->
  <div id="mcp-modal" class="modal hidden">
    <div class="modal-content">
      <h2>MCP Configuration</h2>
      <p>Edit the MCP configuration JSON. This updates ~/.config/Claude/claude_desktop_config.json and restarts MCP.</p>
      <textarea id="mcp-json" style="width:100%; height: 300px; font-family: monospace;"></textarea>
      <div class="actions">
        <button id="mcp-save" class="btn primary">Save</button>
        <button id="mcp-close" class="btn">Close</button>
      </div>
      <div id="mcp-status2"></div>
    </div>
  </div>

  <script src="renderer.js"></script>
</body>
</html>

