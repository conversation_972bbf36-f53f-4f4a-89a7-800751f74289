{"name": "custom-llm-client", "version": "0.1.0", "main": "main.js", "private": true, "type": "commonjs", "scripts": {"build": "node ./scripts/build.js"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.17.5", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.13", "@types/react": "^19.1.12", "@types/react-dom": "^19.1.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.542.0", "react": "^19.1.1", "react-dom": "^19.1.1", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@tailwindcss/cli": "^4.1.13", "@types/node": "^24.3.1", "autoprefixer": "^10.4.21", "esbuild": "^0.23.0", "postcss": "^8.5.6", "tailwindcss": "^4.1.13", "@esbuild-plugins/tsconfig-paths": "^0.1.2"}}