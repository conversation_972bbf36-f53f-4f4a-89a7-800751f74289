#!/usr/bin/env bash
set -euo pipefail

HERE=$(cd "$(dirname "$0")" && pwd)
APPDIR=$(cd "$HERE/.." && pwd)

# Install deps and build (bundled via esbuild)
if command -v npm >/dev/null 2>&1; then
  (cd "$APPDIR" && npm install --no-fund --no-audit)
  (cd "$APPDIR" && npm run build)
else
  echo "npm not found (Node.js tooling missing)."
  exit 1
fi

# Copy renderer assets into dist
mkdir -p "$APPDIR/dist/renderer"
cp -a "$APPDIR/src/renderer/"* "$APPDIR/dist/renderer/"

# Ensure package.json is present in dist (Electron reads main from it)
cp "$APPDIR/package.json" "$APPDIR/dist/package.json"

# Pack to asar in provided destination
DEST=${1:-}
if [ -z "$DEST" ]; then
  echo "Usage: $0 /path/to/output/app.asar"
  exit 1
fi

# Ensure asar is available (use local dev install if present, otherwise rely on build pipeline)
ASAR_JS=""
if [ -f "$APPDIR/../build/node_modules/@electron/asar/bin/asar.mjs" ]; then
  ASAR_JS="$APPDIR/../build/node_modules/@electron/asar/bin/asar.mjs"
elif [ -f "$APPDIR/../build/node_modules/.bin/asar" ]; then
  # Some installs provide a CLI shim; use it directly
  "$APPDIR/../build/node_modules/.bin/asar" pack "$APPDIR/dist" "$DEST"
  echo "Packed custom app via .bin/asar to $DEST"
  exit 0
elif command -v asar >/dev/null 2>&1; then
  asar pack "$APPDIR/dist" "$DEST"
  echo "Packed custom app via global asar to $DEST"
  exit 0
else
  echo "Asar not found. Expected at $APPDIR/../build/node_modules/@electron/asar/bin/asar.mjs or in PATH."
  exit 1
fi

node "$ASAR_JS" pack "$APPDIR/dist" "$DEST"

echo "Packed custom app to $DEST"

