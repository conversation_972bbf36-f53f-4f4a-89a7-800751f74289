#!/usr/bin/env node
const esbuild = require('esbuild');
const { default: TsconfigPathsPlugin } = require('@esbuild-plugins/tsconfig-paths');
const path = require('path');
const fs = require('fs');
const { spawnSync } = require('child_process');

async function build() {
  const outdir = path.join(__dirname, '..', 'dist');
  if (!fs.existsSync(outdir)) fs.mkdirSync(outdir, { recursive: true });

  const common = {
    bundle: true,
    platform: 'node',
    format: 'cjs',
    sourcemap: false,
    // Keep Electron external (provided at runtime), but bundle MCP SDK
    external: ['electron'],
    target: 'node20',
    mainFields: ['main', 'module'],
    conditions: ['node', 'require', 'default']
  };

  await esbuild.build({
    entryPoints: [path.join(__dirname, '..', 'src', 'main.ts')],
    outfile: path.join(outdir, 'main.js'),
    ...common,
  });

  await esbuild.build({
    entryPoints: [path.join(__dirname, '..', 'src', 'preload.ts')],
    outfile: path.join(outdir, 'preload.js'),
    ...common,
  });

  // Build React renderer
  await esbuild.build({
    entryPoints: [path.join(__dirname, '..', 'src', 'renderer', 'index.tsx')],
    outfile: path.join(outdir, 'renderer', 'renderer.js'),
    bundle: true,
    platform: 'browser',
    format: 'iife',
    target: 'es2020',
    jsx: 'automatic',
    jsxImportSource: 'react',
    external: [],
    sourcemap: false,
    plugins: [
      TsconfigPathsPlugin({
        tsconfig: path.join(__dirname, '..', 'tsconfig.json'),
      }),
    ],
  });

  // Process CSS with Tailwind
  console.log('Processing CSS with Tailwind...');
  const tailwindResult = spawnSync('npx', ['tailwindcss', '-i', './src/renderer/globals.css', '-o', './dist/renderer/styles.css'], {
    cwd: path.join(__dirname, '..'),
    stdio: 'inherit',
    shell: true,
  });

  if (tailwindResult.status !== 0) {
    console.error('Failed to process CSS with Tailwind.');
    process.exit(1);
  }

  // Create HTML file
  const htmlContent = `<!doctype html>
<html class="dark">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Claude Desktop</title>
  <link rel="stylesheet" href="styles.css" />
</head>
<body>
  <div id="root"></div>
  <script src="renderer.js"></script>
</body>
</html>`;

  const dstRenderer = path.join(outdir, 'renderer');
  if (!fs.existsSync(dstRenderer)) fs.mkdirSync(dstRenderer, { recursive: true });
  fs.writeFileSync(path.join(dstRenderer, 'index.html'), htmlContent);

  // Copy package.json to dist
  fs.copyFileSync(path.join(__dirname, '..', 'package.json'), path.join(outdir, 'package.json'));

  console.log('Installing production dependencies in dist...');
  const npmInstall = spawnSync('npm', ['install', '--omit=dev'], {
    cwd: outdir,
    stdio: 'inherit',
    shell: true,
  });

  if (npmInstall.status !== 0) {
    console.error('Failed to install production dependencies in dist.');
    process.exit(1);
  }

  console.log('Build complete');
}

build().catch((err) => { console.error(err); process.exit(1); });
