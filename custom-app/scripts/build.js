#!/usr/bin/env node
const esbuild = require('esbuild');
const path = require('path');
const fs = require('fs');
const { spawnSync } = require('child_process');

async function build() {
  const outdir = path.join(__dirname, '..', 'dist');
  if (!fs.existsSync(outdir)) fs.mkdirSync(outdir, { recursive: true });

  const common = {
    bundle: true,
    platform: 'node',
    format: 'cjs',
    sourcemap: false,
    // Keep Electron external (provided at runtime), but bundle MCP SDK
    external: ['electron'],
    target: 'node20',
    mainFields: ['main', 'module'],
    conditions: ['node', 'require', 'default']
  };

  await esbuild.build({
    entryPoints: [path.join(__dirname, '..', 'src', 'main.ts')],
    outfile: path.join(outdir, 'main.js'),
    ...common,
  });

  await esbuild.build({
    entryPoints: [path.join(__dirname, '..', 'src', 'preload.ts')],
    outfile: path.join(outdir, 'preload.js'),
    ...common,
  });

  // Copy renderer assets
  const srcRenderer = path.join(__dirname, '..', 'src', 'renderer');
  const dstRenderer = path.join(outdir, 'renderer');
  if (!fs.existsSync(dstRenderer)) fs.mkdirSync(dstRenderer, { recursive: true });
  for (const file of fs.readdirSync(srcRenderer)) {
    fs.copyFileSync(path.join(srcRenderer, file), path.join(dstRenderer, file));
  }

  // Copy package.json to dist
  fs.copyFileSync(path.join(__dirname, '..', 'package.json'), path.join(outdir, 'package.json'));

  console.log('Installing production dependencies in dist...');
  const npmInstall = spawnSync('npm', ['install', '--omit=dev'], {
    cwd: outdir,
    stdio: 'inherit',
    shell: true,
  });

  if (npmInstall.status !== 0) {
    console.error('Failed to install production dependencies in dist.');
    process.exit(1);
  }

  console.log('Build complete');
}

build().catch((err) => { console.error(err); process.exit(1); });
