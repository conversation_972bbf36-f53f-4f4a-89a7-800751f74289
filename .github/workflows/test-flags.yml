name: Test Build Script Flags (Reusable)

on:
  workflow_call: # Make this workflow reusable
  workflow_dispatch: # Allows manual triggering for testing

jobs:
  test-flags:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    # FUSE install removed - not needed for --test-flags

    - name: Make build script executable
      run: chmod +x ./build.sh

    # Test Case 1: Defaults (deb, clean=yes)
    - name: Test Case 1 - Defaults (deb, clean=yes)
      run: |
        echo "--- Running Test Case 1: ./build.sh --test-flags ---"
        OUTPUT=$(./build.sh --test-flags)
        echo "$OUTPUT" # Print output for logs
        echo "--- Verifying Test Case 1 ---"
        echo "$OUTPUT" | grep -q "Build Format: deb" || (echo "❌ Expected 'Build Format: deb'" && exit 1)
        echo "$OUTPUT" | grep -q "Clean Action: yes" || (echo "❌ Expected 'Clean Action: yes'" && exit 1)
        echo "✓ Test Case 1 Passed"
        # No cleanup needed

    # Test Case 2: --build deb (clean=yes)
    - name: Test Case 2 - --build deb (clean=yes)
      run: |
        echo "--- Running Test Case 2: ./build.sh --build deb --test-flags ---"
        OUTPUT=$(./build.sh --build deb --test-flags)
        echo "$OUTPUT"
        echo "--- Verifying Test Case 2 ---"
        echo "$OUTPUT" | grep -q "Build Format: deb" || (echo "❌ Expected 'Build Format: deb'" && exit 1)
        echo "$OUTPUT" | grep -q "Clean Action: yes" || (echo "❌ Expected 'Clean Action: yes'" && exit 1)
        echo "✓ Test Case 2 Passed"
        # No cleanup needed

    # Test Case 3: --build appimage (clean=yes)
    - name: Test Case 3 - --build appimage (clean=yes)
      run: |
        echo "--- Running Test Case 3: ./build.sh --build appimage --test-flags ---"
        OUTPUT=$(./build.sh --build appimage --test-flags)
        echo "$OUTPUT"
        echo "--- Verifying Test Case 3 ---"
        echo "$OUTPUT" | grep -q "Build Format: appimage" || (echo "❌ Expected 'Build Format: appimage'" && exit 1)
        echo "$OUTPUT" | grep -q "Clean Action: yes" || (echo "❌ Expected 'Clean Action: yes'" && exit 1)
        echo "✓ Test Case 3 Passed"
        # No cleanup needed

    # Test Case 4: --clean yes (build=deb)
    - name: Test Case 4 - --clean yes (build=deb)
      run: |
        echo "--- Running Test Case 4: ./build.sh --clean yes --test-flags ---"
        OUTPUT=$(./build.sh --clean yes --test-flags)
        echo "$OUTPUT"
        echo "--- Verifying Test Case 4 ---"
        echo "$OUTPUT" | grep -q "Build Format: deb" || (echo "❌ Expected 'Build Format: deb'" && exit 1)
        echo "$OUTPUT" | grep -q "Clean Action: yes" || (echo "❌ Expected 'Clean Action: yes'" && exit 1)
        echo "✓ Test Case 4 Passed"
        # No cleanup needed

    # Test Case 5: --clean no (build=deb)
    - name: Test Case 5 - --clean no (build=deb)
      run: |
        echo "--- Running Test Case 5: ./build.sh --clean no --test-flags ---"
        OUTPUT=$(./build.sh --clean no --test-flags)
        echo "$OUTPUT"
        echo "--- Verifying Test Case 5 ---"
        echo "$OUTPUT" | grep -q "Build Format: deb" || (echo "❌ Expected 'Build Format: deb'" && exit 1)
        echo "$OUTPUT" | grep -q "Clean Action: no" || (echo "❌ Expected 'Clean Action: no'" && exit 1)
        echo "✓ Test Case 5 Passed"
        # No cleanup needed

    # Test Case 6: --build deb --clean yes
    - name: Test Case 6 - --build deb --clean yes
      run: |
        echo "--- Running Test Case 6: ./build.sh --build deb --clean yes --test-flags ---"
        OUTPUT=$(./build.sh --build deb --clean yes --test-flags)
        echo "$OUTPUT"
        echo "--- Verifying Test Case 6 ---"
        echo "$OUTPUT" | grep -q "Build Format: deb" || (echo "❌ Expected 'Build Format: deb'" && exit 1)
        echo "$OUTPUT" | grep -q "Clean Action: yes" || (echo "❌ Expected 'Clean Action: yes'" && exit 1)
        echo "✓ Test Case 6 Passed"
        # No cleanup needed

    # Test Case 7: --build deb --clean no
    - name: Test Case 7 - --build deb --clean no
      run: |
        echo "--- Running Test Case 7: ./build.sh --build deb --clean no --test-flags ---"
        OUTPUT=$(./build.sh --build deb --clean no --test-flags)
        echo "$OUTPUT"
        echo "--- Verifying Test Case 7 ---"
        echo "$OUTPUT" | grep -q "Build Format: deb" || (echo "❌ Expected 'Build Format: deb'" && exit 1)
        echo "$OUTPUT" | grep -q "Clean Action: no" || (echo "❌ Expected 'Clean Action: no'" && exit 1)
        echo "✓ Test Case 7 Passed"
        # No cleanup needed

    # Test Case 8: --build appimage --clean yes
    - name: Test Case 8 - --build appimage --clean yes
      run: |
        echo "--- Running Test Case 8: ./build.sh --build appimage --clean yes --test-flags ---"
        OUTPUT=$(./build.sh --build appimage --clean yes --test-flags)
        echo "$OUTPUT"
        echo "--- Verifying Test Case 8 ---"
        echo "$OUTPUT" | grep -q "Build Format: appimage" || (echo "❌ Expected 'Build Format: appimage'" && exit 1)
        echo "$OUTPUT" | grep -q "Clean Action: yes" || (echo "❌ Expected 'Clean Action: yes'" && exit 1)
        echo "✓ Test Case 8 Passed"
        # No cleanup needed

    # Test Case 9: --build appimage --clean no
    - name: Test Case 9 - --build appimage --clean no
      run: |
        echo "--- Running Test Case 9: ./build.sh --build appimage --clean no --test-flags ---"
        OUTPUT=$(./build.sh --build appimage --clean no --test-flags)
        echo "$OUTPUT"
        echo "--- Verifying Test Case 9 ---"
        echo "$OUTPUT" | grep -q "Build Format: appimage" || (echo "❌ Expected 'Build Format: appimage'" && exit 1)
        echo "$OUTPUT" | grep -q "Clean Action: no" || (echo "❌ Expected 'Clean Action: no'" && exit 1)
        echo "✓ Test Case 9 Passed"
        # No cleanup needed
