name: Build Package (Reusable)

on:
  workflow_call:
    inputs:
      build_flags:
        description: 'Flags to pass to build.sh (e.g., "--build appimage --clean no")'
        required: false
        type: string
        default: '' # Default is no extra flags
  workflow_dispatch: # Allows manual triggering for testing

jobs:
  build:
    runs-on: ubuntu-latest # Required, but overridden by caller if specified there

    steps:

    - name: Install FUSE for AppImageTool
      run: |
        sudo apt-get update
        sudo apt-get install -y libfuse2

    - name: Make build script executable
      run: chmod +x ./build.sh

    - name: Run build script
      run: |
        echo "Running build with flags: ${{ inputs.build_flags }}"
        sudo ./build.sh ${{ inputs.build_flags }} # Use sudo for apt install within build.sh

    # Optional: Add steps here to upload the built artifact if needed
    # - name: Upload Artifact
    #   uses: actions/upload-artifact@v4
    #   with:
    #     name: package-${{ inputs.target_arch }}-${{ github.run_id }} # Example name
    #     path: | # Adjust path based on expected output
    #       claude-desktop_*.deb
    #       claude-desktop-*.AppImage
    #     if-no-files-found: error # Fail if build didn't produce output