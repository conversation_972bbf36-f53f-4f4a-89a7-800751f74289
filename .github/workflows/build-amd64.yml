name: Build Package AMD64 (Reusable)

on:
  workflow_call:
    inputs:
      build_flags:
        description: 'Flags to pass to build.sh (e.g., "--build appimage --clean no")'
        required: false
        type: string
        default: '' # Default is no extra flags
      artifact_suffix:
        description: 'Suffix for the artifact name (e.g., deb, appimage)'
        required: true
        type: string

jobs:
  build:
    # Use the standard GitHub-hosted runner for AMD64
    runs-on: ubuntu-latest

    steps:
    # Checkout code is needed within the reusable workflow itself
    # because the caller only specifies 'uses:', it doesn't run steps before calling.
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Install FUSE for AppImageTool
      run: |
        sudo apt-get update
        sudo apt-get install -y libfuse2

    - name: Make build script executable
      run: chmod +x ./build.sh

    - name: Run build script
      run: |
        echo "Running AMD64 build with flags: ${{ inputs.build_flags }}"
        # No sudo needed if runner user has permissions or build.sh handles it internally
        ./build.sh ${{ inputs.build_flags }}

    # Upload the built artifact for the release job
    - name: Upload AMD64 Artifact
      uses: actions/upload-artifact@v4
      with:
        name: package-amd64-${{ inputs.artifact_suffix }} # Unique name using suffix
        path: | # Adjust path based on expected output
          claude-desktop_*.deb
          claude-desktop-*.AppImage
          claude-desktop-*.AppImage.zsync
        if-no-files-found: error # Fail if build didn't produce output