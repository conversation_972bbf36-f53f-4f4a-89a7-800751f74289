name: CI
run-name: |
  CI: ${{
    github.event_name == 'pull_request' && format('PR #{0} by @{1} - {2}', github.event.pull_request.number, github.actor, github.event.pull_request.title) ||
    github.event_name == 'push' && github.event.head_commit && format('Push by @{0} - {1}', github.actor, github.event.head_commit.message) ||
    format('{0} triggered by @{1}', github.event_name, github.actor)
  }}

on:
  push:
    branches:
      # Note: Pushing tags also triggers 'push' events. We filter later with 'if'.
      - main # Trigger on pushes to main
      - dev # Also trigger on pushes to this branch for testing
    paths: # Trigger if relevant files change
      - 'build.sh'
      - 'scripts/**'
      - '.github/workflows/**'
    tags:
      - 'v*' # Also trigger on version tags like v1.0, v2.1.3
  pull_request:
    branches: [main]
  workflow_dispatch: # Allows manual triggering

jobs:

  # First, run the quick flag tests
  test-flags:
    name: Test Flags Parsing
    # Runner is defined in the called workflow (test-flags.yml)
    uses: ./.github/workflows/test-flags.yml
    # No inputs needed for test-flags.yml
 
  # Build AMD64 packages
  build-amd64:
    name: Build Packages (amd64 - ${{ matrix.flags || 'defaults' }})
    needs: test-flags # Run only if test-flags job succeeds
    # Runner is defined in the called workflow (build-amd64.yml)

    strategy:
      fail-fast: false # Allow other builds to continue if one fails
      matrix:
        include:
          - arch: amd64
            flags: "" # Defaults (deb, clean=yes)
            artifact_suffix: 'deb'
          - arch: amd64
            flags: "--build appimage --clean no"
            artifact_suffix: 'appimage'

    uses: ./.github/workflows/build-amd64.yml # Call the dedicated amd64 workflow
    with:
      build_flags: ${{ matrix.flags }}
      artifact_suffix: ${{ matrix.artifact_suffix }}
    # Secrets are not automatically passed to reusable workflows, pass if needed
    # secrets: inherit

  # Build ARM64 packages
  build-arm64:
    name: Build Packages (arm64 - ${{ matrix.flags || 'defaults' }})
    needs: test-flags # Run only if test-flags job succeeds
    # Runner is defined in the called workflow (build-arm64.yml)

    strategy:
      fail-fast: false # Allow other builds to continue if one fails
      matrix:
        include:
          - arch: arm64
            flags: "--clean no" # Implies deb build
            artifact_suffix: 'deb'
          - arch: arm64
            flags: "--build appimage"
            artifact_suffix: 'appimage'

    uses: ./.github/workflows/build-arm64.yml # Call the dedicated arm64 workflow
    with:
      build_flags: ${{ matrix.flags }}
      artifact_suffix: ${{ matrix.artifact_suffix }}
    # Secrets are not automatically passed to reusable workflows, pass if needed
    # secrets: inherit

  # Create Release job - runs only on tag pushes
  release:
    name: Create Release
    # Only run this job if the trigger was a tag push starting with 'v'
    if: startsWith(github.ref, 'refs/tags/v')
    needs: [test-flags, build-amd64, build-arm64] # Ensure builds are successful
    runs-on: ubuntu-latest
    permissions:
      contents: write # Needed to create releases and upload assets

    steps:

      - name: Download AMD64 deb artifact
        uses: actions/download-artifact@v4
        with:
          name: package-amd64-deb
          path: artifacts/ # Download all artifacts into a single directory

      - name: Download AMD64 AppImage artifact
        uses: actions/download-artifact@v4
        with:
          name: package-amd64-appimage
          path: artifacts/

      - name: Download ARM64 deb artifact
        uses: actions/download-artifact@v4
        with:
          name: package-arm64-deb
          path: artifacts/

      - name: Download ARM64 AppImage artifact
        uses: actions/download-artifact@v4
        with:
          name: package-arm64-appimage
          path: artifacts/

      - name: Create GitHub Release
        uses: softprops/action-gh-release@v1
        with:
          files: artifacts/**/* # Upload all downloaded .deb and .AppImage files
          # The release name and tag will automatically use the pushed tag (e.g., v1.0.0)
          # GITHUB_TOKEN is automatically used for authentication
